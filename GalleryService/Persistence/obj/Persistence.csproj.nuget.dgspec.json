{"format": 1, "restore": {"/Users/<USER>/Ventixe/GalleryService/Persistence/Persistence.csproj": {}}, "projects": {"/Users/<USER>/Ventixe/GalleryService/Persistence/Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Ventixe/GalleryService/Persistence/Persistence.csproj", "projectName": "Persistence", "projectPath": "/Users/<USER>/Ventixe/GalleryService/Persistence/Persistence.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Ventixe/GalleryService/Persistence/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}