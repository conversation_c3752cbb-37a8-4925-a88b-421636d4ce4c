using Persistence.Entities;

namespace Persistence.Interfaces;

public interface IGalleryRepository
{
    Task<List<GalleryItem>> GetAllAsync();
    Task<GalleryItem?> GetByIdAsync(string id);
    Task<List<GalleryItem>> GetByCategoryAsync(string category);
    Task<List<GalleryItem>> GetFeaturedAsync();
    Task<List<GalleryItem>> SearchAsync(string searchTerm);
    Task<GalleryItem> CreateAsync(GalleryItem item);
    Task<GalleryItem?> UpdateAsync(string id, GalleryItem item);
    Task<bool> DeleteAsync(string id);
    Task<List<string>> GetCategoriesAsync();
}
