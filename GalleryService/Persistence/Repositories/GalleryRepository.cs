using System.Text.Json;
using Persistence.Contexts;
using Persistence.Entities;
using Persistence.Interfaces;

namespace Persistence.Repositories;

public class GalleryRepository : IGalleryRepository
{
    private readonly GalleryDbContext _context;
    private static readonly List<GalleryItem> _mockData = GenerateMockData();

    public GalleryRepository(GalleryDbContext context)
    {
        _context = context;
    }

    public async Task<List<GalleryItem>> GetAllAsync()
    {
        await Task.Delay(50); // Simulate async operation
        return _mockData.OrderByDescending(x => x.CreatedAt).ToList();
    }

    public async Task<GalleryItem?> GetByIdAsync(string id)
    {
        await Task.Delay(50);
        return _mockData.FirstOrDefault(x => x.Id == id);
    }

    public async Task<List<GalleryItem>> GetByCategoryAsync(string category)
    {
        await Task.Delay(50);
        return _mockData
            .Where(x => x.Category.Equals(category, StringComparison.OrdinalIgnoreCase))
            .OrderByDescending(x => x.CreatedAt)
            .ToList();
    }

    public async Task<List<GalleryItem>> GetFeaturedAsync()
    {
        await Task.Delay(50);
        return _mockData.Where(x => x.IsFeatured).OrderByDescending(x => x.CreatedAt).ToList();
    }

    public async Task<List<GalleryItem>> SearchAsync(string searchTerm)
    {
        await Task.Delay(50);
        var term = searchTerm.ToLower();
        return _mockData
            .Where(x =>
                x.Title.ToLower().Contains(term)
                || x.Description.ToLower().Contains(term)
                || x.Category.ToLower().Contains(term)
                || x.Location.ToLower().Contains(term)
            )
            .OrderByDescending(x => x.CreatedAt)
            .ToList();
    }

    public async Task<GalleryItem> CreateAsync(GalleryItem item)
    {
        await Task.Delay(100);
        item.Id = Guid.NewGuid().ToString();
        item.CreatedAt = DateTime.UtcNow;
        item.UpdatedAt = DateTime.UtcNow;
        _mockData.Add(item);
        return item;
    }

    public async Task<GalleryItem?> UpdateAsync(string id, GalleryItem item)
    {
        await Task.Delay(100);
        var existing = _mockData.FirstOrDefault(x => x.Id == id);
        if (existing == null)
            return null;

        existing.Title = item.Title;
        existing.Description = item.Description;
        existing.ImageUrl = item.ImageUrl;
        existing.Category = item.Category;
        existing.CategoryColor = item.CategoryColor;
        existing.EventDate = item.EventDate;
        existing.Location = item.Location;
        existing.Price = item.Price;
        existing.Currency = item.Currency;
        existing.Tags = item.Tags;
        existing.IsFeatured = item.IsFeatured;
        existing.UpdatedAt = DateTime.UtcNow;

        return existing;
    }

    public async Task<bool> DeleteAsync(string id)
    {
        await Task.Delay(50);
        var item = _mockData.FirstOrDefault(x => x.Id == id);
        if (item == null)
            return false;

        _mockData.Remove(item);
        return true;
    }

    public async Task<List<string>> GetCategoriesAsync()
    {
        await Task.Delay(50);
        return _mockData.Select(x => x.Category).Distinct().OrderBy(x => x).ToList();
    }

    private static List<GalleryItem> GenerateMockData()
    {
        var categories = new[]
        {
            new { Name = "Music", Color = "#FF6B6B" },
            new { Name = "Food & Culinary", Color = "#4ECDC4" },
            new { Name = "Art & Design", Color = "#45B7D1" },
            new { Name = "Technology", Color = "#96CEB4" },
            new { Name = "Fashion", Color = "#FFEAA7" },
            new { Name = "Health & Wellness", Color = "#DDA0DD" },
            new { Name = "Outdoor & Adventure", Color = "#98D8C8" },
            new { Name = "Sports", Color = "#F7DC6F" },
        };

        return new List<GalleryItem>
        {
            new()
            {
                Id = "1",
                Title = "Echo Beats Festival",
                Description =
                    "An electrifying music festival featuring top artists from around the world. Experience incredible live performances, amazing food, and unforgettable memories.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop",
                Category = "Music",
                CategoryColor = "#FF6B6B",
                EventDate = new DateTime(2029, 5, 20),
                Location = "Central Park, New York",
                Status = "Active",
                AttendeeCount = 15000,
                Price = 89.99m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(new[] { "festival", "music", "outdoor", "live" }),
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-5),
                IsFeatured = true,
                ViewCount = 2847,
                Rating = 4.8,
                ReviewCount = 342,
            },
            new()
            {
                Id = "2",
                Title = "Culinary Delights Festival",
                Description =
                    "A gastronomic journey featuring world-renowned chefs, cooking demonstrations, and tastings from diverse culinary traditions.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop",
                Category = "Food & Culinary",
                CategoryColor = "#4ECDC4",
                EventDate = new DateTime(2029, 5, 25),
                Location = "Convention Center, Chicago",
                Status = "Active",
                AttendeeCount = 8500,
                Price = 65.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(new[] { "food", "cooking", "chef", "tasting" }),
                CreatedAt = DateTime.UtcNow.AddDays(-25),
                UpdatedAt = DateTime.UtcNow.AddDays(-3),
                IsFeatured = true,
                ViewCount = 1923,
                Rating = 4.6,
                ReviewCount = 187,
            },
            new()
            {
                Id = "3",
                Title = "Artistry Unveiled Expo",
                Description =
                    "Discover contemporary art from emerging and established artists. Interactive installations, live painting, and exclusive gallery tours.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=300&fit=crop",
                Category = "Art & Design",
                CategoryColor = "#45B7D1",
                EventDate = new DateTime(2029, 5, 15),
                Location = "Modern Art Museum, Los Angeles",
                Status = "Active",
                AttendeeCount = 3200,
                Price = 45.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(
                    new[] { "art", "gallery", "contemporary", "exhibition" }
                ),
                CreatedAt = DateTime.UtcNow.AddDays(-20),
                UpdatedAt = DateTime.UtcNow.AddDays(-2),
                IsFeatured = false,
                ViewCount = 1456,
                Rating = 4.4,
                ReviewCount = 98,
            },
            new()
            {
                Id = "4",
                Title = "Tech Future Expo",
                Description =
                    "Explore cutting-edge technology, AI innovations, and the future of digital transformation. Network with industry leaders and tech enthusiasts.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=300&fit=crop",
                Category = "Technology",
                CategoryColor = "#96CEB4",
                EventDate = new DateTime(2029, 6, 1),
                Location = "Tech Hub, San Francisco",
                Status = "Active",
                AttendeeCount = 12000,
                Price = 120.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(
                    new[] { "technology", "AI", "innovation", "networking" }
                ),
                CreatedAt = DateTime.UtcNow.AddDays(-18),
                UpdatedAt = DateTime.UtcNow.AddDays(-1),
                IsFeatured = true,
                ViewCount = 3421,
                Rating = 4.9,
                ReviewCount = 256,
            },
            new()
            {
                Id = "5",
                Title = "Runway Revolution 2029",
                Description =
                    "The latest fashion trends and designer collections. Witness stunning runway shows and meet renowned fashion designers.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400&h=300&fit=crop",
                Category = "Fashion",
                CategoryColor = "#FFEAA7",
                EventDate = new DateTime(2029, 5, 1),
                Location = "Fashion District, Milan",
                Status = "Active",
                AttendeeCount = 2800,
                Price = 95.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(
                    new[] { "fashion", "runway", "designer", "trends" }
                ),
                CreatedAt = DateTime.UtcNow.AddDays(-15),
                UpdatedAt = DateTime.UtcNow.AddDays(-4),
                IsFeatured = false,
                ViewCount = 1876,
                Rating = 4.3,
                ReviewCount = 142,
            },
            new()
            {
                Id = "6",
                Title = "Global Wellness Summit",
                Description =
                    "Focus on mental health, fitness, and holistic wellness. Expert speakers, yoga sessions, and wellness workshops.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1506629905607-d9c8e3b8e0e4?w=400&h=300&fit=crop",
                Category = "Health & Wellness",
                CategoryColor = "#DDA0DD",
                EventDate = new DateTime(2029, 5, 5),
                Location = "Wellness Center, Denver",
                Status = "Active",
                AttendeeCount = 1500,
                Price = 75.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(
                    new[] { "wellness", "health", "yoga", "mindfulness" }
                ),
                CreatedAt = DateTime.UtcNow.AddDays(-12),
                UpdatedAt = DateTime.UtcNow.AddDays(-6),
                IsFeatured = false,
                ViewCount = 987,
                Rating = 4.7,
                ReviewCount = 89,
            },
            new()
            {
                Id = "7",
                Title = "Adventure Gear Show",
                Description =
                    "Discover the latest outdoor equipment, adventure gear, and extreme sports innovations. Meet professional adventurers and gear experts.",
                ImageUrl =
                    "https://images.unsplash.com/photo-**********-561732d1e306?w=400&h=300&fit=crop",
                Category = "Outdoor & Adventure",
                CategoryColor = "#98D8C8",
                EventDate = new DateTime(2029, 6, 5),
                Location = "Outdoor Expo Center, Colorado",
                Status = "Active",
                AttendeeCount = 4200,
                Price = 55.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(new[] { "outdoor", "adventure", "gear", "sports" }),
                CreatedAt = DateTime.UtcNow.AddDays(-10),
                UpdatedAt = DateTime.UtcNow.AddDays(-7),
                IsFeatured = false,
                ViewCount = 1234,
                Rating = 4.5,
                ReviewCount = 156,
            },
            new()
            {
                Id = "8",
                Title = "Symphony Under the Stars",
                Description =
                    "An enchanting evening of classical music performed under the open sky. World-class orchestra and soloists in a magical outdoor setting.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop",
                Category = "Music",
                CategoryColor = "#FF6B6B",
                EventDate = new DateTime(2029, 4, 20),
                Location = "Amphitheater, Boston",
                Status = "Active",
                AttendeeCount = 5000,
                Price = 85.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(
                    new[] { "classical", "orchestra", "outdoor", "symphony" }
                ),
                CreatedAt = DateTime.UtcNow.AddDays(-8),
                UpdatedAt = DateTime.UtcNow.AddDays(-8),
                IsFeatured = false,
                ViewCount = 1567,
                Rating = 4.8,
                ReviewCount = 203,
            },
            new()
            {
                Id = "9",
                Title = "Harmony Health Fair",
                Description =
                    "Comprehensive health screenings, wellness workshops, and holistic healing demonstrations. Connect with health professionals and wellness experts.",
                ImageUrl =
                    "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop",
                Category = "Health & Wellness",
                CategoryColor = "#DDA0DD",
                EventDate = new DateTime(2029, 6, 15),
                Location = "Community Center, Portland",
                Status = "Active",
                AttendeeCount = 2100,
                Price = 25.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(
                    new[] { "health", "wellness", "screening", "community" }
                ),
                CreatedAt = DateTime.UtcNow.AddDays(-6),
                UpdatedAt = DateTime.UtcNow.AddDays(-6),
                IsFeatured = false,
                ViewCount = 876,
                Rating = 4.6,
                ReviewCount = 67,
            },
            new()
            {
                Id = "10",
                Title = "Live Paint Battle",
                Description =
                    "Watch talented artists compete in real-time painting challenges. Interactive art creation, live voting, and creative workshops.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1460661419201-fd4cecdf8a8b?w=400&h=300&fit=crop",
                Category = "Art & Design",
                CategoryColor = "#45B7D1",
                EventDate = new DateTime(2029, 6, 20),
                Location = "Art District, Austin",
                Status = "Active",
                AttendeeCount = 1800,
                Price = 35.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(
                    new[] { "art", "competition", "live", "interactive" }
                ),
                CreatedAt = DateTime.UtcNow.AddDays(-4),
                UpdatedAt = DateTime.UtcNow.AddDays(-4),
                IsFeatured = false,
                ViewCount = 1123,
                Rating = 4.4,
                ReviewCount = 94,
            },
            new()
            {
                Id = "11",
                Title = "Spring Trends Runway",
                Description =
                    "Showcase of spring fashion collections from emerging designers. Sustainable fashion focus with eco-friendly materials and ethical production.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=400&h=300&fit=crop",
                Category = "Fashion",
                CategoryColor = "#FFEAA7",
                EventDate = new DateTime(2029, 6, 10),
                Location = "Fashion Week Venue, Paris",
                Status = "Active",
                AttendeeCount = 3500,
                Price = 110.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(
                    new[] { "fashion", "sustainable", "spring", "runway" }
                ),
                CreatedAt = DateTime.UtcNow.AddDays(-2),
                UpdatedAt = DateTime.UtcNow.AddDays(-2),
                IsFeatured = false,
                ViewCount = 1789,
                Rating = 4.2,
                ReviewCount = 128,
            },
            new()
            {
                Id = "12",
                Title = "Champions League Final Viewing Party",
                Description =
                    "Watch the biggest football match of the year on giant screens with fellow fans. Food, drinks, and an electric atmosphere guaranteed.",
                ImageUrl =
                    "https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400&h=300&fit=crop",
                Category = "Sports",
                CategoryColor = "#F7DC6F",
                EventDate = new DateTime(2029, 5, 10),
                Location = "Sports Bar & Grill, Manchester",
                Status = "Active",
                AttendeeCount = 800,
                Price = 15.00m,
                Currency = "USD",
                Tags = JsonSerializer.Serialize(
                    new[] { "sports", "football", "viewing", "social" }
                ),
                CreatedAt = DateTime.UtcNow.AddDays(-1),
                UpdatedAt = DateTime.UtcNow.AddDays(-1),
                IsFeatured = true,
                ViewCount = 2341,
                Rating = 4.7,
                ReviewCount = 189,
            },
        };
    }
}
