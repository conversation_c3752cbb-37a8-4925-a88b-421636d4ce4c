using System.ComponentModel.DataAnnotations;

namespace Persistence.Entities;

public class GalleryItem
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(1000)]
    public string Description { get; set; } = string.Empty;
    
    [Required]
    public string ImageUrl { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Category { get; set; } = string.Empty;
    
    [MaxLength(7)]
    public string CategoryColor { get; set; } = string.Empty;
    
    [Required]
    public DateTime EventDate { get; set; }
    
    [Required]
    [MaxLength(200)]
    public string Location { get; set; } = string.Empty;
    
    [MaxLength(20)]
    public string Status { get; set; } = "Active";
    
    public int AttendeeCount { get; set; }
    
    public decimal Price { get; set; }
    
    [MaxLength(3)]
    public string Currency { get; set; } = "USD";
    
    public string Tags { get; set; } = string.Empty; // JSON string
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public bool IsFeatured { get; set; } = false;
    
    public int ViewCount { get; set; } = 0;
    
    public double Rating { get; set; } = 0.0;
    
    public int ReviewCount { get; set; } = 0;
}
