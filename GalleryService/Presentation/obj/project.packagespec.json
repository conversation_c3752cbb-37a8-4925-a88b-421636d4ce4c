﻿"restore":{"projectUniqueName":"/Users/<USER>/Ventixe/GalleryService/Presentation/Presentation.csproj","projectName":"Presentation","projectPath":"/Users/<USER>/Ventixe/GalleryService/Presentation/Presentation.csproj","outputPath":"/Users/<USER>/Ventixe/GalleryService/Presentation/obj/","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"/Users/<USER>/Ventixe/GalleryService/Application/Application.csproj":{"projectPath":"/Users/<USER>/Ventixe/GalleryService/Application/Application.csproj"},"/Users/<USER>/Ventixe/GalleryService/Persistence/Persistence.csproj":{"projectPath":"/Users/<USER>/Ventixe/GalleryService/Persistence/Persistence.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.200"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.AspNetCore.OpenApi":{"target":"Package","version":"[9.0.2, )"},"Microsoft.EntityFrameworkCore.InMemory":{"target":"Package","version":"[9.0.0, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[7.2.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"/usr/local/share/dotnet/sdk/9.0.200/PortableRuntimeIdentifierGraph.json"}}