using Application.Interfaces;
using Application.Models;
using Microsoft.AspNetCore.Mvc;

namespace Presentation.Controllers;

[Route("api/[controller]")]
[ApiController]
public class GalleryController : ControllerBase
{
    private readonly IGalleryService _galleryService;

    public GalleryController(IGalleryService galleryService)
    {
        _galleryService = galleryService;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await _galleryService.GetGalleryItemsAsync();
        return result.Success ? Ok(result) : StatusCode(500, result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(string id)
    {
        var result = await _galleryService.GetGalleryItemAsync(id);
        return result.Success ? Ok(result) : NotFound(result);
    }

    [HttpGet("category/{category}")]
    public async Task<IActionResult> GetByCategory(string category)
    {
        var result = await _galleryService.GetGalleryItemsByCategoryAsync(category);
        return result.Success ? Ok(result) : StatusCode(500, result);
    }

    [HttpGet("featured")]
    public async Task<IActionResult> GetFeatured()
    {
        var result = await _galleryService.GetFeaturedGalleryItemsAsync();
        return result.Success ? Ok(result) : StatusCode(500, result);
    }

    [HttpGet("search")]
    public async Task<IActionResult> Search([FromQuery] string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return BadRequest(new { Success = false, Error = "Search term is required" });
        }

        var result = await _galleryService.SearchGalleryItemsAsync(searchTerm);
        return result.Success ? Ok(result) : StatusCode(500, result);
    }

    [HttpGet("categories")]
    public async Task<IActionResult> GetCategories()
    {
        var result = await _galleryService.GetCategoriesAsync();
        return result.Success ? Ok(result) : StatusCode(500, result);
    }

    [HttpPost]
    public async Task<IActionResult> Create(CreateGalleryItemRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(new
            {
                Success = false,
                Error = "Invalid data",
                Details = ModelState
            });
        }

        var result = await _galleryService.CreateGalleryItemAsync(request);
        return result.Success 
            ? CreatedAtAction(nameof(GetById), new { id = result.Data!.Id }, result)
            : StatusCode(500, result);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(string id, CreateGalleryItemRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(new
            {
                Success = false,
                Error = "Invalid data",
                Details = ModelState
            });
        }

        var result = await _galleryService.UpdateGalleryItemAsync(id, request);
        return result.Success ? Ok(result) : NotFound(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(string id)
    {
        var result = await _galleryService.DeleteGalleryItemAsync(id);
        return result.Success ? Ok(result) : NotFound(result);
    }
}
