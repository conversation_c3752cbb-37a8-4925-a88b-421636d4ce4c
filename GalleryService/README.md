# Ventixe GalleryService

A .NET 9 microservice for managing gallery items and event displays in the Ventixe platform, providing comprehensive gallery management capabilities with rich visual content.

## 🌐 Live Demo

**🚀 Deployed API**: Coming soon - ready for Azure deployment

**📖 API Documentation**: `/swagger` (available after deployment)

## 📋 Overview

The GalleryService is a RESTful API microservice built with .NET 9 that manages gallery items for the Ventixe platform. It provides endpoints for creating, reading, updating, and deleting gallery items with various filtering and search capabilities. The service comes pre-loaded with rich mock data representing various event types with beautiful imagery.

## 🛠️ Technologies Used

- **.NET 9** - Latest version of .NET framework
- **ASP.NET Core** - Web API framework
- **Entity Framework Core** - Object-Relational Mapping (ORM)
- **In-Memory Database** - For development and testing
- **Swagger/OpenAPI** - API documentation
- **Clean Architecture** - Layered architecture pattern

## 🏗️ Architecture

The service follows Clean Architecture principles with clear separation of concerns:

```
GalleryService/
├── Presentation/           # API controllers and configuration
│   ├── Controllers/        # REST API controllers
│   └── Program.cs         # Application entry point
├── Application/           # Business logic and services
│   ├── Interfaces/        # Service contracts
│   ├── Models/           # DTOs and view models
│   └── Services/         # Business logic implementation
└── Persistence/          # Data access layer
    ├── Contexts/         # Entity Framework DbContext
    ├── Entities/         # Database entities
    ├── Interfaces/       # Repository contracts
    └── Repositories/     # Data access implementation
```

## 🔧 Setup Instructions

### Prerequisites

- .NET 9 SDK
- Visual Studio 2022 or VS Code
- Git

### Local Development

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd GalleryService
   ```

2. **Restore dependencies**
   ```bash
   dotnet restore
   ```

3. **Build the solution**
   ```bash
   dotnet build
   ```

4. **Run the application**
   ```bash
   cd Presentation
   dotnet run
   ```

5. **Access the API**
   Navigate to `https://localhost:7003/swagger` (or the displayed URL)

## 📡 API Endpoints

### Gallery Management

| Method | Endpoint                    | Description                |
| ------ | --------------------------- | -------------------------- |
| `GET`  | `/api/gallery`              | Get all gallery items      |
| `GET`  | `/api/gallery/{id}`         | Get gallery item by ID     |
| `GET`  | `/api/gallery/category/{category}` | Get items by category |
| `GET`  | `/api/gallery/featured`     | Get featured items         |
| `GET`  | `/api/gallery/search?searchTerm={term}` | Search gallery items |
| `GET`  | `/api/gallery/categories`   | Get all categories         |
| `POST` | `/api/gallery`              | Create new gallery item    |
| `PUT`  | `/api/gallery/{id}`         | Update gallery item        |
| `DELETE` | `/api/gallery/{id}`       | Delete gallery item        |

### Request/Response Examples

#### Get All Gallery Items

```http
GET /api/gallery
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "title": "Echo Beats Festival",
      "description": "An electrifying music festival featuring top artists...",
      "imageUrl": "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f",
      "category": "Music",
      "categoryColor": "#FF6B6B",
      "eventDate": "2029-05-20T00:00:00",
      "formattedDate": "May 20, 2029",
      "location": "Central Park, New York",
      "status": "Active",
      "attendeeCount": 15000,
      "price": 89.99,
      "currency": "USD",
      "tags": ["festival", "music", "outdoor", "live"],
      "isFeatured": true,
      "viewCount": 2847,
      "rating": 4.8,
      "reviewCount": 342
    }
  ]
}
```

#### Create Gallery Item

```http
POST /api/gallery
Content-Type: application/json

{
  "title": "New Art Exhibition",
  "description": "Contemporary art showcase featuring emerging artists",
  "imageUrl": "https://example.com/image.jpg",
  "category": "Art & Design",
  "eventDate": "2029-07-15T00:00:00",
  "location": "Art Gallery, NYC",
  "price": 25.00,
  "currency": "USD",
  "tags": ["art", "exhibition", "contemporary"],
  "isFeatured": false
}
```

## 🗃️ Data Models

### GalleryItemDto
- **Id**: Unique identifier
- **Title**: Event/item title
- **Description**: Detailed description
- **ImageUrl**: URL to the main image
- **Category**: Event category (Music, Art & Design, etc.)
- **CategoryColor**: Hex color for category display
- **EventDate**: Date of the event
- **FormattedDate**: Human-readable date format
- **Location**: Event location
- **Status**: Current status (Active, Inactive)
- **AttendeeCount**: Number of attendees
- **Price**: Ticket price
- **Currency**: Price currency
- **Tags**: Array of descriptive tags
- **IsFeatured**: Whether item is featured
- **ViewCount**: Number of views
- **Rating**: Average rating (0-5)
- **ReviewCount**: Number of reviews

## 🎨 Categories

The service supports the following categories with associated colors:

- **Music** (#FF6B6B) - Concerts, festivals, live performances
- **Food & Culinary** (#4ECDC4) - Food festivals, cooking events
- **Art & Design** (#45B7D1) - Art exhibitions, design showcases
- **Technology** (#96CEB4) - Tech conferences, innovation events
- **Fashion** (#FFEAA7) - Fashion shows, style events
- **Health & Wellness** (#DDA0DD) - Wellness events, health fairs
- **Outdoor & Adventure** (#98D8C8) - Outdoor activities, adventure events
- **Sports** (#F7DC6F) - Sports events, competitions

## 🔌 Integration

### Frontend Integration

The service integrates with the Ventixe Frontend application:

```javascript
// Frontend service usage
import { galleryService } from "./services/galleryService";

// Get all gallery items
const items = await galleryService.getAllGalleryItems();

// Get items by category
const musicEvents = await galleryService.getGalleryItemsByCategory("Music");

// Search gallery items
const searchResults = await galleryService.searchGalleryItems("festival");
```

### Microservices Integration

- **EventService**: Gallery items can be linked to events
- **FeedbackService**: Collect feedback for gallery items
- **InvoiceService**: Handle payments for gallery events

## 🚀 Deployment

### Azure Deployment

The service is ready for Azure deployment with:

- **Azure App Service** configuration
- **CORS** enabled for frontend integration
- **Swagger** documentation available
- **Health checks** implemented

### Environment Configuration

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

## 🧪 Testing

Run tests with:

```bash
dotnet test
```

## 📊 Mock Data

The service includes rich mock data with:
- 12 diverse gallery items
- 8 different categories
- High-quality Unsplash images
- Realistic event data
- Various price points and locations

## 🔧 Configuration

### Development Configuration (appsettings.Development.json)

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Service Registration Errors**
   - Ensure all services are properly registered in Program.cs
   - Check project references are correct

2. **CORS Issues**
   - Verify CORS policy is configured correctly
   - Check frontend URL is allowed

3. **Image Loading Issues**
   - Verify image URLs are accessible
   - Check network connectivity

## 📝 License

This project is part of the Ventixe platform and follows the same licensing terms.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions, please contact the Ventixe development team.
