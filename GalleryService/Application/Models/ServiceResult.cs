namespace Application.Models;

public class ServiceResult<T>
{
    public bool Success { get; set; }
    public T? Data { get; set; }
    public string Error { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;

    public static ServiceResult<T> SuccessResult(T data, string message = "")
    {
        return new ServiceResult<T>
        {
            Success = true,
            Data = data,
            Message = message
        };
    }

    public static ServiceResult<T> ErrorResult(string error)
    {
        return new ServiceResult<T>
        {
            Success = false,
            Error = error
        };
    }
}

public class ServiceResult
{
    public bool Success { get; set; }
    public string Error { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;

    public static ServiceResult SuccessResult(string message = "")
    {
        return new ServiceResult
        {
            Success = true,
            Message = message
        };
    }

    public static ServiceResult ErrorResult(string error)
    {
        return new ServiceResult
        {
            Success = false,
            Error = error
        };
    }
}
