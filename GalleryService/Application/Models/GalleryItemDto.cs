namespace Application.Models;

public class GalleryItemDto
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ImageUrl { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string CategoryColor { get; set; } = string.Empty;
    public DateTime EventDate { get; set; }
    public string FormattedDate { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int AttendeeCount { get; set; }
    public decimal Price { get; set; }
    public string Currency { get; set; } = "USD";
    public List<string> Tags { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsFeatured { get; set; }
    public int ViewCount { get; set; }
    public double Rating { get; set; }
    public int ReviewCount { get; set; }
}
