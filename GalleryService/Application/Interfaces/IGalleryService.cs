using Application.Models;

namespace Application.Interfaces;

public interface IGalleryService
{
    Task<ServiceResult<List<GalleryItemDto>>> GetGalleryItemsAsync();
    Task<ServiceResult<GalleryItemDto>> GetGalleryItemAsync(string id);
    Task<ServiceResult<List<GalleryItemDto>>> GetGalleryItemsByCategoryAsync(string category);
    Task<ServiceResult<List<GalleryItemDto>>> GetFeaturedGalleryItemsAsync();
    Task<ServiceResult<List<GalleryItemDto>>> SearchGalleryItemsAsync(string searchTerm);
    Task<ServiceResult<GalleryItemDto>> CreateGalleryItemAsync(CreateGalleryItemRequest request);
    Task<ServiceResult<GalleryItemDto>> UpdateGalleryItemAsync(string id, CreateGalleryItemRequest request);
    Task<ServiceResult> DeleteGalleryItemAsync(string id);
    Task<ServiceResult<List<string>>> GetCategoriesAsync();
}
