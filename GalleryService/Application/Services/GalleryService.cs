using Application.Interfaces;
using Application.Models;
using Persistence.Entities;
using Persistence.Interfaces;
using System.Text.Json;

namespace Application.Services;

public class GalleryService : IGalleryService
{
    private readonly IGalleryRepository _repository;

    public GalleryService(IGalleryRepository repository)
    {
        _repository = repository;
    }

    public async Task<ServiceResult<List<GalleryItemDto>>> GetGalleryItemsAsync()
    {
        try
        {
            var items = await _repository.GetAllAsync();
            var dtos = items.Select(MapToDto).ToList();
            return ServiceResult<List<GalleryItemDto>>.SuccessResult(dtos);
        }
        catch (Exception ex)
        {
            return ServiceResult<List<GalleryItemDto>>.ErrorResult($"Error retrieving gallery items: {ex.Message}");
        }
    }

    public async Task<ServiceResult<GalleryItemDto>> GetGalleryItemAsync(string id)
    {
        try
        {
            var item = await _repository.GetByIdAsync(id);
            if (item == null)
            {
                return ServiceResult<GalleryItemDto>.ErrorResult("Gallery item not found");
            }

            var dto = MapToDto(item);
            return ServiceResult<GalleryItemDto>.SuccessResult(dto);
        }
        catch (Exception ex)
        {
            return ServiceResult<GalleryItemDto>.ErrorResult($"Error retrieving gallery item: {ex.Message}");
        }
    }

    public async Task<ServiceResult<List<GalleryItemDto>>> GetGalleryItemsByCategoryAsync(string category)
    {
        try
        {
            var items = await _repository.GetByCategoryAsync(category);
            var dtos = items.Select(MapToDto).ToList();
            return ServiceResult<List<GalleryItemDto>>.SuccessResult(dtos);
        }
        catch (Exception ex)
        {
            return ServiceResult<List<GalleryItemDto>>.ErrorResult($"Error retrieving gallery items by category: {ex.Message}");
        }
    }

    public async Task<ServiceResult<List<GalleryItemDto>>> GetFeaturedGalleryItemsAsync()
    {
        try
        {
            var items = await _repository.GetFeaturedAsync();
            var dtos = items.Select(MapToDto).ToList();
            return ServiceResult<List<GalleryItemDto>>.SuccessResult(dtos);
        }
        catch (Exception ex)
        {
            return ServiceResult<List<GalleryItemDto>>.ErrorResult($"Error retrieving featured gallery items: {ex.Message}");
        }
    }

    public async Task<ServiceResult<List<GalleryItemDto>>> SearchGalleryItemsAsync(string searchTerm)
    {
        try
        {
            var items = await _repository.SearchAsync(searchTerm);
            var dtos = items.Select(MapToDto).ToList();
            return ServiceResult<List<GalleryItemDto>>.SuccessResult(dtos);
        }
        catch (Exception ex)
        {
            return ServiceResult<List<GalleryItemDto>>.ErrorResult($"Error searching gallery items: {ex.Message}");
        }
    }

    public async Task<ServiceResult<GalleryItemDto>> CreateGalleryItemAsync(CreateGalleryItemRequest request)
    {
        try
        {
            var entity = MapToEntity(request);
            var createdItem = await _repository.CreateAsync(entity);
            var dto = MapToDto(createdItem);
            return ServiceResult<GalleryItemDto>.SuccessResult(dto, "Gallery item created successfully");
        }
        catch (Exception ex)
        {
            return ServiceResult<GalleryItemDto>.ErrorResult($"Error creating gallery item: {ex.Message}");
        }
    }

    public async Task<ServiceResult<GalleryItemDto>> UpdateGalleryItemAsync(string id, CreateGalleryItemRequest request)
    {
        try
        {
            var entity = MapToEntity(request);
            var updatedItem = await _repository.UpdateAsync(id, entity);
            if (updatedItem == null)
            {
                return ServiceResult<GalleryItemDto>.ErrorResult("Gallery item not found");
            }

            var dto = MapToDto(updatedItem);
            return ServiceResult<GalleryItemDto>.SuccessResult(dto, "Gallery item updated successfully");
        }
        catch (Exception ex)
        {
            return ServiceResult<GalleryItemDto>.ErrorResult($"Error updating gallery item: {ex.Message}");
        }
    }

    public async Task<ServiceResult> DeleteGalleryItemAsync(string id)
    {
        try
        {
            var success = await _repository.DeleteAsync(id);
            if (!success)
            {
                return ServiceResult.ErrorResult("Gallery item not found");
            }

            return ServiceResult.SuccessResult("Gallery item deleted successfully");
        }
        catch (Exception ex)
        {
            return ServiceResult.ErrorResult($"Error deleting gallery item: {ex.Message}");
        }
    }

    public async Task<ServiceResult<List<string>>> GetCategoriesAsync()
    {
        try
        {
            var categories = await _repository.GetCategoriesAsync();
            return ServiceResult<List<string>>.SuccessResult(categories);
        }
        catch (Exception ex)
        {
            return ServiceResult<List<string>>.ErrorResult($"Error retrieving categories: {ex.Message}");
        }
    }

    private static GalleryItemDto MapToDto(GalleryItem entity)
    {
        var tags = new List<string>();
        if (!string.IsNullOrEmpty(entity.Tags))
        {
            try
            {
                tags = JsonSerializer.Deserialize<List<string>>(entity.Tags) ?? new List<string>();
            }
            catch
            {
                // If deserialization fails, return empty list
                tags = new List<string>();
            }
        }

        return new GalleryItemDto
        {
            Id = entity.Id,
            Title = entity.Title,
            Description = entity.Description,
            ImageUrl = entity.ImageUrl,
            Category = entity.Category,
            CategoryColor = entity.CategoryColor,
            EventDate = entity.EventDate,
            FormattedDate = entity.EventDate.ToString("MMM dd, yyyy"),
            Location = entity.Location,
            Status = entity.Status,
            AttendeeCount = entity.AttendeeCount,
            Price = entity.Price,
            Currency = entity.Currency,
            Tags = tags,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt,
            IsFeatured = entity.IsFeatured,
            ViewCount = entity.ViewCount,
            Rating = entity.Rating,
            ReviewCount = entity.ReviewCount
        };
    }

    private static GalleryItem MapToEntity(CreateGalleryItemRequest request)
    {
        var categoryColors = new Dictionary<string, string>
        {
            { "Music", "#FF6B6B" },
            { "Food & Culinary", "#4ECDC4" },
            { "Art & Design", "#45B7D1" },
            { "Technology", "#96CEB4" },
            { "Fashion", "#FFEAA7" },
            { "Health & Wellness", "#DDA0DD" },
            { "Outdoor & Adventure", "#98D8C8" },
            { "Sports", "#F7DC6F" }
        };

        return new GalleryItem
        {
            Title = request.Title,
            Description = request.Description,
            ImageUrl = request.ImageUrl,
            Category = request.Category,
            CategoryColor = categoryColors.GetValueOrDefault(request.Category, "#6C757D"),
            EventDate = request.EventDate,
            Location = request.Location,
            Price = request.Price,
            Currency = request.Currency,
            Tags = JsonSerializer.Serialize(request.Tags),
            IsFeatured = request.IsFeatured,
            Status = "Active",
            AttendeeCount = 0,
            ViewCount = 0,
            Rating = 0.0,
            ReviewCount = 0
        };
    }
}
